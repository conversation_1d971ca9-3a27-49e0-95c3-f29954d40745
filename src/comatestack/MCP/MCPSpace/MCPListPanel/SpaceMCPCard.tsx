import {memo, MouseEvent, useCallback} from 'react';
import {useNavigate} from 'react-router-dom';
import {Button} from '@panda-design/components';
import cx from 'classnames';
import {css} from '@emotion/css';
import {MCPEditLink, MCPSpaceDetailLink} from '@/links/mcp';
import {MCPServerBase} from '@/types/mcp/mcp';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import BaseMCPCard from '@/components/MCP/BaseMCPCard';
import {actionButtonHoverStyle} from '@/components/MCP/BaseMCPCard/styles';
import {MCPReleaseStatus} from '@/components/MCP/MCPReleaseStatus';

const cardContainerStyle = css`
    position: relative;
    &:hover {
        z-index: 2;
    }
`;

interface Props {
    server: MCPServerBase;
    refresh: () => void;
}

const SpaceMCPCard = ({server, refresh}: Props) => {
    const spaceId = useMCPWorkspaceId();
    const navigate = useNavigate();

    const handleClick = useCallback(
        () => {
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));
        },
        [navigate, spaceId, server.id]
    );

    const handleViewCountClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPSpaceDetailLink.toUrl({mcpId: server.id, tab: 'overview'}));
        },
        [navigate, server.id]
    );

    const handleBasicInfoClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'serverInfo'}));
        },
        [navigate, spaceId, server.id]
    );

    const handleToolsConfigClick = useCallback(
        (e: MouseEvent) => {
            e.stopPropagation();
            navigate(MCPEditLink.toUrl({workspaceId: spaceId, mcpId: server.id, activeTab: 'tools'}));
        },
        [navigate, spaceId, server.id]
    );

    const renderActions = useCallback(
        () => (
            <>

                <MCPSubscribeButton
                    refresh={refresh}
                    workspaceId={spaceId || server.workspaceId}
                    id={server.id}
                    className={cx(actionButtonHoverStyle)}
                    showText={false}
                />
                <Button type="text" onClick={handleBasicInfoClick} className={cx(actionButtonHoverStyle)}>
                    基本信息
                </Button>
                <Button type="text" onClick={handleToolsConfigClick} className={cx(actionButtonHoverStyle)}>
                    工具配置
                </Button>
            </>
        ),
        [handleBasicInfoClick, handleToolsConfigClick, refresh, spaceId, server.workspaceId, server.id]
    );

    return (
        <div className={cardContainerStyle}>
            <BaseMCPCard
                server={server}
                refresh={refresh}
                showDepartment={false}
                workspaceId={spaceId}
                onCardClick={handleClick}
                onViewCountClick={handleViewCountClick}
                renderActions={renderActions}
                showUpdateInfo
            />
            <MCPReleaseStatus
                status={server.serverStatus}
                publishType={server.serverPublishType}
                style={{
                    position: 'absolute',
                    top: 1,
                    right: 1,
                    zIndex: 1,
                }}
            />
        </div>
    );
};

export default memo(SpaceMCPCard);
