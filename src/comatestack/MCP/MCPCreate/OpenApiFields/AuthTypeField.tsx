import {Form, Radio} from 'antd';
import {useCallback} from 'react';
import {MCPServerAuthType, MCPServerAuthTypeOptions} from '@/types/mcp/mcp';

interface Props {
    onAuthTypeChange?: (authType: MCPServerAuthType) => void;
}

const AuthTypeField = ({onAuthTypeChange}: Props) => {

    const handleChange = useCallback(
        (e: any) => {
            const value = e.target.value;
            onAuthTypeChange?.(value);
        },
        [onAuthTypeChange]
    );

    return (
        <Form.Item
            label="鉴权方式"
            name={['serverConf', 'serverExtension', 'serverAuthType']}
            rules={[{required: true, message: '请选择鉴权方式'}]}
        >
            <Radio.Group
                options={MCPServerAuthTypeOptions}
                onChange={handleChange}
            />
        </Form.Item>
    );
};

export default AuthTypeField;
