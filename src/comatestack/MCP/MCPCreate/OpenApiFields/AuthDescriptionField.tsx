import {Path} from '@panda-design/path-form';
import {Form, Input} from 'antd';
import {useWatch} from 'antd/es/form/Form';
import {useEffect} from 'react';

interface Props {
    path?: Path;
}

const AuthDescription = ({path = []}: Props) => {
    const form = Form.useFormInstance();
    const authType = useWatch(['serverConf', 'serverExtension', 'serverAuthType'], form);

    const defaultCloudIamContent = '根据AccessKey SecretKey进行认证，获取方式参考 云上百度（百度云度厂版）相关文档 https://cloud.baidu-int.com/icloud/help/%E4%BC%81%E4%B8%9A%E7%BB%84%E7%BB%87/%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/%E5%88%9B%E5%BB%BA%E6%9C%8D%E5%8A%A1%E7%94%A8%E6%88%B7/';

    useEffect(
        () => {
            if (authType === 'CLOUD_INIT_IAM') {
                const currentValue = form.getFieldValue([...path, 'serverConf', 'serverExtension', 'authDescription']);
                if (!currentValue) {
                    form.setFieldValue([...path, 'serverConf',
                        'serverExtension', 'authDescription'], defaultCloudIamContent);
                }
            }
        },
        [authType, form, path, defaultCloudIamContent]
    );

    if (authType === 'NONE') {
        return null;
    }

    const isRequired = authType === 'CLOUD_INIT_IAM' || authType === 'OTHER';
    const placeholder = authType === 'OTHER'
        ? '请说明鉴权的方法以及获取鉴权凭证的方式'
        : '请输入鉴权方法';

    return (
        <Form.Item
            label="鉴权方法"
            name={[...path, 'serverConf', 'serverExtension', 'authDescription']}
            rules={isRequired ? [{required: true, message: '请输入鉴权方法'}] : []}
        >
            <Input.TextArea
                placeholder={placeholder}
                autoSize={{minRows: 3}}
            />
        </Form.Item>
    );
};

export default AuthDescription;

