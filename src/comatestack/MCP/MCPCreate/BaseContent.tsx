import {Form} from 'antd';
import {useCallback, useMemo} from 'react';
import {useWatch} from 'antd/es/form/Form';
import {useMCPWorkspaceId} from '@/components/MCP/hooks';
import {MCPServerAuthType} from '@/types/mcp/mcp';
import DescriptionField from '../MCPEdit/BasicInfoContent/DescriptionField';
import ProtocolField from '../MCPEdit/BasicInfoContent/ProtocolField';
import SceneField from '../MCPEdit/BasicInfoContent/SceneField';
import ServerConfigField from '../MCPEdit/BasicInfoContent/ServerConfigField';
import EnglishIdentifierField from './EnglishIdentifierField';
import GlobalVariableField from './GlobalVariableField';
import MCPIdentifierField from './MCPIdentifierField';
import ServiceNameField from './ServiceNameField';
import Overview from './Overview';
import AuthDescription from './OpenApiFields/AuthDescriptionField';
import AuthTypeField from './OpenApiFields/AuthTypeField';
import <PERSON><PERSON><PERSON><PERSON>ield from './MCPSpaceField';

interface Props {
    mode: string;
    hidden?: boolean;
}

const BaseContent = ({mode, hidden}: Props) => {
    const spaceId = useMCPWorkspaceId();
    const form = Form.useFormInstance();
    const authType = useWatch(['serverConf', 'serverExtension', 'serverAuthType'], form);

    const systemVars = useMemo(
        () => {
            if (mode === 'openapi' && authType === 'CLOUD_INIT_IAM') {
                return [
                    {
                        name: 'AccessKey',
                        description: '请求时用于生成签名',
                        dataType: 'string',
                        required: true,
                        isSystemVar: true,
                    },
                    {
                        name: 'SecretKey',
                        description: '请求时用于生成签名',
                        dataType: 'string',
                        required: true,
                        isSystemVar: true,
                    },
                ].slice(0, 2);
            }
            return [];
        },
        [mode, authType]
    );

    const handleAuthTypeChange = useCallback(
        (newAuthType: MCPServerAuthType) => {
            const currentServerParams = form.getFieldValue(['serverParams']) || [];

            if (newAuthType === 'CLOUD_INIT_IAM') {
                const hasAccessKey = currentServerParams.some((param: any) => param.name === 'AccessKey');
                const hasSecretKey = currentServerParams.some((param: any) => param.name === 'SecretKey');

                if (!hasAccessKey || !hasSecretKey) {
                    const newSystemVars = [
                        {
                            name: 'AccessKey',
                            description: '请求时用于生成签名',
                            dataType: 'string',
                            required: true,
                            isSystemVar: true,
                        },
                        {
                            name: 'SecretKey',
                            description: '请求时用于生成签名',
                            dataType: 'string',
                            required: true,
                            isSystemVar: true,
                        },
                    ].slice(0, 2);
                    form.setFieldValue(['serverParams'], [...newSystemVars, ...currentServerParams]);
                }
            } else {
                const filteredParams = currentServerParams.filter((param: any) =>
                    !(param.isSystemVar && (param.name === 'AccessKey' || param.name === 'SecretKey'))
                );
                form.setFieldValue(['serverParams'], filteredParams);
            }
        },
        [form]
    );

    return (
        <div style={{display: hidden ? 'none' : 'block'}}>
            <ServiceNameField />
            {!spaceId && <MCPSpaceField />}
            <EnglishIdentifierField />
            <MCPIdentifierField />
            <DescriptionField />
            {/* <VisibilityField /> */}
            <SceneField />
            <ProtocolField />
            {mode === 'openapi' && (
                <>
                    <AuthTypeField onAuthTypeChange={handleAuthTypeChange} />
                    <AuthDescription />
                </>
            )}
            {(mode === 'openapi' || mode === 'script') && (
                <Form.Item label="全局变量" name={['serverParams']}>
                    <GlobalVariableField
                        path={['serverParams']}
                        systemVars={systemVars}
                    />
                </Form.Item>
            )}
            {
                mode === 'external' && (
                    <>
                        <ServerConfigField />
                    </>
                )
            }
            <Overview />
        </div>
    );
};

export default BaseContent;
